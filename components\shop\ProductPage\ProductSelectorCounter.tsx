"use client"
import CustomButton from "@/components/UI/CustomButton"
import { Plus, Minus, ShoppingBasket } from "lucide-react"
import { useState, useEffect, useRef, useMemo } from "react"
import { Variation } from "@/lib/types/product.types"
import { useCart } from "@/lib/context/cart-context"

interface ProductSelectorCounterProps {
    selectedVariant?: Variation | null;
    productInfo?: {
        image?: string;
        name?: string;
    };
}

const ProductSelectorCounter = ({ selectedVariant, productInfo }: ProductSelectorCounterProps) => {
    const [isSticky, setIsSticky] = useState(false)
    const sectionRef = useRef<HTMLDivElement>(null)


    const { cartItems, totalItems, finalPrice, isUpdating, addToCart: addItemToCart, decreaseFromCart } = useCart()
    // Calculate the quantity of the currently selected variant in the cart
    // Using useMemo to recalculate when cartItems or selectedVariant changes
    const currentVariantQuantity = useMemo(() => {

        return selectedVariant
            ? cartItems.find(item => item.id === selectedVariant.id)?.quantity || 0
            : 0;
    }, [cartItems, selectedVariant]);
    cartItems.find(item => console.log(item.id === selectedVariant?.id)
    )
    // console.warn(cartItems);
    // console.warn(selectedVariant);
    // console.warn(currentVariantQuantity);


    useEffect(() => {
        const handleScroll = () => {
            if (sectionRef.current) {
                const sectionRect = sectionRef.current.getBoundingClientRect()
                // Show sticky when section is scrolled past (top of section is above viewport top)
                setIsSticky(sectionRect.top < 0)
            }
        }


        window.addEventListener('scroll', handleScroll)
        return () => window.removeEventListener('scroll', handleScroll)
    }, [])


    const addToCart = async () => {
        if (selectedVariant) {
            addItemToCart(selectedVariant, 1, productInfo);
        }
    };

    // Decrease quantity or remove the selected variant from cart
    const handleDecreaseFromCart = () => {
        if (selectedVariant) {
            // Use the decreaseFromCart function from the cart context
            decreaseFromCart(selectedVariant);
        }
    };

    return (
        <>
            <div className="mb-3 flex gap-3">
                <span>
                    قیمت نهایی:
                </span>
                <span className="text-primary font-bold">
                    {finalPrice.toLocaleString()} تومان
                </span>
            </div>
            {/* Original non-sticky version */}
            <div ref={sectionRef}>
                <div className={`flex items-center border border-gray-300 rounded-2xl px-5 py-3 w-full ${isUpdating ? 'opacity-70' : ''}`}>
                    <button
                        className="bg-[#2DC058] disabled:bg-[#C5CBD4]/50 rounded-full flex items-center justify-center w-10 h-8"
                        onClick={addToCart}
                        disabled={isUpdating || !selectedVariant}
                    >
                        {isUpdating ? (
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        ) : (
                            <Plus size={16} color="white" />
                        )}
                    </button>
                    <input
                        type="tel"
                        dir="rtl"
                        className="left-direction text-center w-full bg-transparent text-lg font-semibold focus:outline-none"
                        value={currentVariantQuantity.toLocaleString()}
                        maxLength={8}
                        readOnly
                    />
                    <button
                        className="bg-[#C5CBD4] disabled:bg-[#C5CBD4]/50 rounded-full flex items-center justify-center w-10 h-8"
                        onClick={handleDecreaseFromCart}
                        disabled={currentVariantQuantity === 0 || isUpdating}
                    >
                        {isUpdating ? (
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        ) : (
                            <Minus size={16} color="white" />
                        )}
                    </button>
                </div>
                <CustomButton
                    className='py-5 flex items-center mt-5 w-full justify-center gap-2'
                    onClick={addToCart}
                    disabled={isUpdating || !selectedVariant}
                    loading={isUpdating}
                >
                    <ShoppingBasket />
                    <span>افزودن به سبد خرید</span>
                    {/* نمایش تعداد محصولات در سبد خرید */}
                    {totalItems > 0 && !isUpdating && (
                        <span className="bg-white text-green-600 rounded-full w-6 h-6 flex items-center justify-center text-sm">
                            {totalItems}
                        </span>
                    )}
                </CustomButton>
            </div>

            {/* Sticky version - only shown when scrolled past section */}
            {isSticky && (
                <div className="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 p-4 z-50 md:hidden">
                    <div className="container mx-auto flex items-center justify-between gap-4">
                        <div className={`flex items-center border border-gray-300 rounded-2xl px-3 py-2 flex-1 max-w-[200px] ${isUpdating ? 'opacity-70' : ''}`}>
                            <button
                                className="bg-[#2DC058] disabled:bg-[#C5CBD4]/50 rounded-full flex items-center justify-center w-8 h-6"
                                onClick={addToCart}
                                disabled={isUpdating || !selectedVariant}
                            >
                                {isUpdating ? (
                                    <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                ) : (
                                    <Plus size={14} color="white" />
                                )}
                            </button>
                            <input
                                type="tel"
                                dir="rtl"
                                className="left-direction text-center w-full bg-transparent text-base font-semibold focus:outline-none"
                                value={currentVariantQuantity.toLocaleString()}
                                maxLength={8}
                                readOnly
                            />
                            <button
                                className="bg-[#C5CBD4] disabled:bg-[#C5CBD4]/50 rounded-full flex items-center justify-center w-8 h-6"
                                onClick={handleDecreaseFromCart}
                                disabled={currentVariantQuantity === 0 || isUpdating}
                            >
                                {isUpdating ? (
                                    <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                ) : (
                                    <Minus size={14} color="white" />
                                )}
                            </button>
                        </div>

                        <CustomButton
                            className='py-3 flex items-center justify-center gap-2 flex-1'
                            onClick={addToCart}
                            disabled={isUpdating || !selectedVariant}
                            loading={isUpdating}
                        >
                            <ShoppingBasket size={18} />
                            <span className="text-sm">سبد خرید</span>
                            {/* نمایش تعداد محصولات در سبد خرید */}
                            {totalItems > 0 && !isUpdating && (
                                <span className="bg-white text-green-600 rounded-full w-5 h-5 flex items-center justify-center text-xs">
                                    {totalItems}
                                </span>
                            )}
                        </CustomButton>
                    </div>
                </div>
            )}
        </>
    )
}

export default ProductSelectorCounter
