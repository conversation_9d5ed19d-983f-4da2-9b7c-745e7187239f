"use client"

import { useState } from 'react';
import { ShieldCheck } from 'lucide-react';
import CatalogProductCard from './CatalogProductCard';
import ProductCardInfo from './ProductCardInfo';
import ProviderShopInfo from './ProviderShopInfo';
import { AttributeValue, ProductData, Variation } from '@/lib/types/product.types';
import RatingStars from './RattingStars';

const ProductCartDetails = ({ productData }: { productData: ProductData }) => {
    /**
     * Find default color from attributes based on default variant
     */
    const findDefaultColor = (): AttributeValue | null => {
        if (!productData.default_variant || !productData.default_variant.color) return null;

        const colorAttributes = productData.attributes.filter(attr => attr.type === 'color');
        if (colorAttributes.length === 0) return null;

        const defaultColorValue = colorAttributes[0].values.find(
            color => color.value.toLowerCase() === productData.default_variant.color.toLowerCase()
        );

        return defaultColorValue || null;
    };

    /**
     * Find default secondary attribute (size or other) from attributes based on default variant
     * @returns {string | null} The default secondary attribute value or null
     */
    const findDefaultSecondaryAttribute = (): string | null => {
        if (!productData.default_variant) return null;

        // Find the first non-color attribute that has values
        const secondaryAttribute = productData.attributes.find(attr =>
            attr.type !== 'color' && attr.values.length > 0
        );

        if (!secondaryAttribute) return null;

        // For size, check the size property of default_variant
        if (secondaryAttribute.type === 'size' && productData.default_variant.size) {
            return productData.default_variant.size;
        }

        // For other attributes, try to find a matching value
        return null;
    };

    
    const [selectedColor, setSelectedColor] = useState<AttributeValue | null>(() => findDefaultColor());
    const [selectedSecondaryAttribute, setSelectedSecondaryAttribute] = useState<string | null>(() => findDefaultSecondaryAttribute());
    const [selectedVariant, setSelectedVariant] = useState<Variation | null>(() => {
        return productData.default_variant || null;
    });

    console.log('ProductData:', productData);
    console.log('Default variant:', productData.default_variant);
    console.log('Selected variant:', selectedVariant);

    /**
     * Find matching variant based on color and secondary attribute (size or other)
     * @param {AttributeValue | null} color - Selected color attribute
     * @param {string | null} secondaryAttr - Selected secondary attribute value
     * @returns {Variation | null} Matching variation or null
     */
    const findMatchingVariant = (color: AttributeValue | null, secondaryAttr: string | null): Variation | null => {
        if (!color || !productData.variations || productData.variations.length === 0) {
            return productData.default_variant || null;
        }

        let matchingVariants = productData.variations.filter(
            variant => variant.color.toLowerCase() === color.value.toLowerCase()
        );

        // If secondary attribute is selected, filter by it (usually size)
        if (secondaryAttr) {
            matchingVariants = matchingVariants.filter(
                variant => variant.size === secondaryAttr
            );
        }

        return matchingVariants.length > 0 ? matchingVariants[0] : (productData.default_variant || null);
    };

    

    /**
     * Handle color selection and update variant accordingly
     * @param {AttributeValue | null} color - Selected color attribute
     */
    const handleColorSelect = (color: AttributeValue | null) => {
        setSelectedColor(color);

        // Reset secondary attribute when color changes
        setSelectedSecondaryAttribute(null);

        // Find a variant that matches the selected color
        const matchingVariant = findMatchingVariant(color, null);
        setSelectedVariant(matchingVariant);

        if (matchingVariant) {
            console.log('Found matching variant by color:', matchingVariant);
        }
    };

    /**
     * Handle secondary attribute selection (size or other) and update variant accordingly
     */
    const handleSecondaryAttributeSelect = (secondaryAttr: string | null) => {
        setSelectedSecondaryAttribute(secondaryAttr);

        // Find a variant that matches both color and secondary attribute
        const matchingVariant = findMatchingVariant(selectedColor, secondaryAttr);
        setSelectedVariant(matchingVariant);

        if (matchingVariant) {
            console.log('Found matching variant by color and secondary attribute:', matchingVariant);
        }
    };

    return (
        <section className='flex md:p-7 max-md:p-3 max-md:pt-6 mt-10 bg-white rounded-3xl md:justify-between gap-5 mb-10 max-md:flex-col max-md:w-full'>
            <CatalogProductCard gallery={productData.galleries} />
            <div className='md:w-[50%]'>
                <h2 className='flex items-center gap-2'>
                    <ShieldCheck fill='#9DA5B0' color='white' /> ضمانت کیفت و اصالت کالا
                </h2>
                <div className='flex items-center md:gap-3 max-md:gap-1 mb-4'>
                    <div className='review flex gap-2 items-center h-full my-2 max-md:my-3'>
                        <RatingStars rate={productData.product_rate || 0} />

                        <span className='text-sm max-md:text-xs max-md:whitespace-nowrap'> (توسط {productData.product_rates_count} مشتری) </span>
                    </div>
                    <span className='bg-gray-100 text-sm px-3 py-2 max-md:px-2 max-md:whitespace-nowrap max-md:text-xs rounded-3xl'>
                        {productData.comments_count} دیدگاه

                    </span>
                    <span className='bg-gray-100 text-sm px-3 py-2 max-md:px-2 max-md:whitespace-nowrap max-md:text-xs rounded-3xl'>
                        {productData.questions_count}  پرسش
                    </span>
                </div>
                <h1 className='md:text-xl mb-5'>
                    {productData.title}
                </h1>
                <ProductCardInfo
                    {...productData}
                    onChange={handleColorSelect}
                    onSizeChange={handleSecondaryAttributeSelect}
                    selectedColor={selectedColor}
                    selectedSize={selectedSecondaryAttribute}
                    selectedVariant={selectedVariant}
                />
            </div>
            <ProviderShopInfo
                shop={productData.shop}
                variations={productData.variations}
                selectedColor={selectedColor}
                selectedSize={selectedSecondaryAttribute}
                selectedVariant={selectedVariant}
                productInfo={{
                    image: productData.galleries?.[0]?.url,
                    name: productData.title
                }}
                guarantees={productData.guarantees}
            />
        </section>
    )
}

export default ProductCartDetails