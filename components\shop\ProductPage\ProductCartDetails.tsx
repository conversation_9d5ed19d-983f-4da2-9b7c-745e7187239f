"use client"

import { useEffect, useState } from 'react';
import { ShieldCheck } from 'lucide-react';
import CatalogProductCard from './CatalogProductCard';
import ProductCardInfo from './ProductCardInfo';
import ProviderShopInfo from './ProviderShopInfo';
import { AttributeValue, ProductData, Variation } from '@/lib/types/product.types';
import RatingStars from './RattingStars';

const ProductCartDetails = ({ productData }: { productData: ProductData }) => {
    // Find default color from attributes based on default variant
    const findDefaultColor = (): AttributeValue | null => {
        if (!productData.default_variant || !productData.default_variant.color) return null;

        const colorAttributes = productData.attributes.filter(attr => attr.type === 'color');
        if (colorAttributes.length === 0) return null;

        const defaultColorValue = colorAttributes[0].values.find(
            color => color.value.toLowerCase() === productData.default_variant.color.toLowerCase()
        );

        return defaultColorValue || null;
    };

debugger
    const [selectedColor, setSelectedColor] = useState<AttributeValue | null>(findDefaultColor);
    const [selectedSize, setSelectedSize] = useState<string | null>(
        productData.default_variant ? productData.default_variant.size : null
    );
    const [selectedVariant, setSelectedVariant] = useState<Variation | null>(productData.default_variant || null);
    console.log(productData);
    
    // Find matching variant based on color and size
    const findMatchingVariant = (color: AttributeValue | null, size: string | null) => {
        if (!color || !productData.variations || productData.variations.length === 0) {
            return null;
        }

        let matchingVariants = productData.variations.filter(
            variant => variant.color.toLowerCase() === color.value.toLowerCase()
        );

        // If size is selected, filter by size
        if (size) {
            matchingVariants = matchingVariants.filter(
                variant => variant.size === size
            );
        }

        return matchingVariants.length > 0 ? matchingVariants[0] : null;
    };
    useEffect(() => {
      first
    
      return () => {
        second
      }
    }, [se])
    

    // Handle color selection
    const handleColorSelect = (color: AttributeValue | null) => {
        setSelectedColor(color);

        // Reset size when color changes
        setSelectedSize(selectedSize);

        // Find a variant that matches the selected color
        const matchingVariant = findMatchingVariant(color, null);
        setSelectedVariant(matchingVariant);

        if (matchingVariant) {
            console.log('Found matching variant by color:', matchingVariant);
        }
    };

    // Handle size selection
    const handleSizeSelect = (size: string | null) => {
        setSelectedSize(size);

        // Find a variant that matches both color and size
        const matchingVariant = findMatchingVariant(selectedColor, size);
        setSelectedVariant(matchingVariant);

        if (matchingVariant) {
            console.log('Found matching variant by color and size:', matchingVariant);
        }
    };

    return (
        <section className='flex md:p-7 max-md:p-3 max-md:pt-6 mt-10 bg-white rounded-3xl md:justify-between gap-5 mb-10 max-md:flex-col max-md:w-full'>
            <CatalogProductCard gallery={productData.galleries} />
            <div className='md:w-[50%]'>
                <h2 className='flex items-center gap-2'>
                    <ShieldCheck fill='#9DA5B0' color='white' /> ضمانت کیفت و اصالت کالا
                </h2>
                <div className='flex items-center md:gap-3 max-md:gap-1 mb-4'>
                    <div className='review flex gap-2 items-center h-full my-2 max-md:my-3'>
                        <RatingStars rate={productData.product_rate || 0} />

                        <span className='text-sm max-md:text-xs max-md:whitespace-nowrap'> (توسط {productData.product_rates_count} مشتری) </span>
                    </div>
                    <span className='bg-gray-100 text-sm px-3 py-2 max-md:px-2 max-md:whitespace-nowrap max-md:text-xs rounded-3xl'>
                        {productData.comments_count} دیدگاه

                    </span>
                    <span className='bg-gray-100 text-sm px-3 py-2 max-md:px-2 max-md:whitespace-nowrap max-md:text-xs rounded-3xl'>
                        {productData.questions_count}  پرسش
                    </span>
                </div>
                <h1 className='md:text-xl mb-5'>
                    {productData.title}
                </h1>
                <ProductCardInfo
                    {...productData}
                    onChange={handleColorSelect}
                    onSizeChange={handleSizeSelect}
                    selectedColor={selectedColor}
                    selectedSize={selectedSize}
                    selectedVariant={selectedVariant}
                />
            </div>
            <ProviderShopInfo
                shop={productData.shop}
                variations={productData.variations}
                selectedColor={selectedColor}
                selectedSize={selectedSize}
                selectedVariant={selectedVariant}
                productInfo={{
                    image: productData.galleries?.[0]?.url,
                    name: productData.title
                }}
            />
        </section>
    )
}

export default ProductCartDetails