import Image from "next/image"
import PaymentSuccess from "@/public/assets/images/payment-success.png"
import { InvoiceResponse } from "@/lib/types/invoice.types"
import { X } from "lucide-react"
const OrderStatusHeader = ({ orderStatus }: { orderStatus: InvoiceResponse }) => {
    const orderStatusData = orderStatus?.data
    console.log(orderStatus);

    return (
        <div className={`bg-gradient-to-b ${orderStatusData?.status == "paid" ? "from-[#D8ECC9]" : "from-red-300"} to-transparent flex flex-col justify-center items-center rounded-3xl`}>
            <div className="my-8">
                
                {orderStatusData?.status == "paid" ? (
                   <div className="flex justify-center">
                    <Image src={PaymentSuccess} alt="payment-success" />
                </div>
                ) : (
                    <div className="bg-red-700 rounded-full p-5 flex justify-center items-center w-20 h-20 mx-auto mb-5">
                        <X size={35} className='stroke-white' />
                    </div>
                )}
                <div>
                    <h2 className="text-xl max-md:text-lg">
                        {orderStatusData?.status == "paid" ? "سفارش شما با موفقیت ثبت شد" : "سفارش شما ثبت نشد"}
                    </h2>
                </div>
            </div>

            <div className="flex justify-around w-full max-md:flex-wrap text-sm max-md:text-xs max-md:gap-y-3">
                <div className="w-[20%] max-md:w-[30%]">
                    <p className="flex flex-col gap-3 border-[3px] border-dashed rounded-xl px-3.5 py-4 bg-white">
                        <span>
                            شماره سفارش
                        </span>
                        <span className="text-yellow text-xs">
                            #{orderStatusData.id}
                        </span>
                    </p>
                </div>
                <div className="w-[18%] max-md:w-[30%]">
                    <p className="flex flex-col gap-3 border-[3px] border-dashed rounded-xl px-3.5 py-4 bg-white">
                        <span>
                            تعداد کالا
                        </span>
                        <span className="text-primary">
                            {orderStatusData.products.length} مورد
                        </span>
                    </p>
                </div>

                <div className="w-[18%] max-md:w-[30%]">
                    <p className="flex flex-col gap-3 border-[3px] border-dashed rounded-xl px-3.5 py-4 bg-white">
                        <span>
                            تاریخ ثبت
                        </span>
                        <span className="">
                            {orderStatusData.creation_date}
                        </span>
                    </p>
                </div>
                <div className="w-[18%] max-md:w-full max-md:px-1">
                    <p className="flex flex-col gap-3 border-[3px] border-dashed rounded-xl px-3.5 py-4 bg-white max-md:justify-center max-md:items-center">
                        <span>
                            مبلغ پرداختی
                        </span>
                        <span className="text-red-500 max-md:text-base">
                            {orderStatusData.total.toLocaleString()} تومان
                        </span>
                    </p>
                </div>

            </div>
        </div>
    )
}

export default OrderStatusHeader